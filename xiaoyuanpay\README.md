# 小园缴费平台系统 (XiaoYuanPay)

## 项目简介

小园缴费平台是一个面向学校的企业级缴费管理系统，支持多种缴费场景，包括学费、杂费、兴趣班、活动费等。系统采用前后端分离架构，提供管理端和移动端两种访问方式。

## 技术栈

### 后端技术
- Spring Boot 2.7.18
- MyBatis 3.5.13 + MyBatis-Plus *******
- MySQL 8.0.35
- Redis 6.2.14
- RocketMQ 4.9.7
- Spring Security 5.7.x + JWT
- Swagger 3.0.0 + Knife4j 4.3.0

### 前端技术
- 管理端：Vue 3.3.4 + Vite 4.4.9 + Element Plus 2.3.12 + TypeScript 5.1.6
- 移动端：Uni-App 3.8.12 + uView UI 2.0.36
- 可视化：ECharts 5.4.3 + D3.js 7.8.5
- 状态管理：Pinia 2.1.6
- HTTP客户端：Axios 1.5.0

## 项目结构

```
xiaoyuanpay/
├── backend/                 # 后端服务
│   ├── src/
│   ├── pom.xml
│   └── README.md
├── frontend-admin/         # 管理端前端
│   ├── src/
│   ├── package.json
│   └── README.md
├── frontend-mobile/        # 移动端前端
│   ├── src/
│   ├── package.json
│   └── README.md
├── docs/                   # 项目文档
│   ├── api/               # API文档
│   ├── database/          # 数据库设计文档
│   └── deployment/        # 部署文档
├── scripts/               # 脚本文件
│   ├── init-db.sql       # 数据库初始化脚本
│   └── deploy.sh         # 部署脚本
└── README.md             # 项目说明
```

## 功能特性

### 核心功能
- **多角色权限管理**：支持超级管理员、财务管理员、班主任、家长等多种角色
- **灵活的缴费项目**：支持固定费用、选择性费用、临时性费用等多种类型
- **兴趣班管理**：时间冲突检测、互斥课程单选、实时库存管理
- **购物车式支付**：支持多项目合并支付，积分抵扣
- **多渠道支付**：支持微信支付、支付宝、银联等多种支付方式
- **退费管理**：完整的退费申请工作流
- **积分系统**：积分获取、使用、转赠功能

### 技术特性
- 前后端分离架构
- RESTful API设计
- JWT认证授权
- Redis缓存优化
- 消息队列异步处理
- 容器化部署支持

## 快速开始

### 环境要求
- JDK 1.8+
- Node.js 16+
- MySQL 8.0+
- Redis 6.2+
- Maven 3.6+

### 后端启动
```bash
cd backend
mvn clean install
mvn spring-boot:run
```

### 管理端启动
```bash
cd frontend-admin
npm install
npm run dev
```

### 移动端启动
```bash
cd frontend-mobile
npm install
npm run dev:h5
```

## 开发规范

- 遵循阿里巴巴Java开发手册
- 前端代码使用ESLint + Prettier规范
- Git提交信息遵循Conventional Commits规范
- 所有API必须编写接口文档

## 许可证

本项目采用 MIT 许可证