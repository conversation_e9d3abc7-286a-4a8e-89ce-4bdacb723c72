spring:
  datasource:
    url: **********************************************************************************************************************************************************
  
  redis:
    host: localhost
    port: 6379
    password: 

logging:
  level:
    com.xiaoyuan.pay: DEBUG
    org.springframework.security: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: ./logs/xiaoyuanpay.log

knife4j:
  production: false