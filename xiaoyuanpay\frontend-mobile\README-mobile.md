# 小园缴费平台 - 移动端

## 技术栈

- Uni-App 3.8.12
- Vue 3.4.21
- uView UI 2.0.36
- TypeScript

## 项目结构

```
src/
├── api/            # API接口
├── components/     # 公共组件
├── pages/          # 页面
├── static/         # 静态资源
├── store/          # 状态管理
├── utils/          # 工具函数
├── App.vue         # 应用配置
├── main.ts         # 入口文件
├── manifest.json   # 应用配置
├── pages.json      # 页面配置
└── uni.scss        # 全局样式变量
```

## 开发

```bash
# 安装依赖
npm install

# H5开发
npm run dev:h5

# 微信小程序开发
npm run dev:mp-weixin

# 构建H5
npm run build:h5

# 构建微信小程序
npm run build:mp-weixin
```

## 功能模块

- 扫码入口页
- 用户登录注册
- 身份绑定
- 缴费项目列表
- 购物车
- 支付流程
- 订单管理
- 个人中心
- 积分管理

## 注意事项

- 遵循 uni-app 开发规范
- 注意平台差异化处理
- 使用条件编译处理不同平台