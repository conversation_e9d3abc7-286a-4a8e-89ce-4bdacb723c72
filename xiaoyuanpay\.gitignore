# IDE
.idea/
.vscode/
*.iml
.DS_Store

# Backend
backend/target/
backend/*.log
backend/.mvn/
backend/mvnw
backend/mvnw.cmd

# Frontend
node_modules/
dist/
.env.local
.env.*.local
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# Frontend Admin
frontend-admin/dist/
frontend-admin/node_modules/
frontend-admin/.vite/

# Frontend Mobile
frontend-mobile/dist/
frontend-mobile/node_modules/
frontend-mobile/unpackage/

# Logs
logs/
*.log

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Test coverage
coverage/
.nyc_output/

# Dependency directories
jspm_packages/

# Build files
*.class
*.jar
*.war
*.ear

# Package files
*.tar.gz
*.zip

# Database
*.db
*.sqlite

# Temporary files
*.tmp
*.temp
.cache/

# Security
*.key
*.pem
*.p12
.env
application-local.yml
application-local.properties