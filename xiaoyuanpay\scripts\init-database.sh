#!/bin/bash

# 数据库连接信息
MYSQL_HOST="localhost"
MYSQL_PORT="3306"
MYSQL_USER="root"
MYSQL_PASSWORD="root"
DATABASE_NAME="xiaoyuanpay_dev"

echo "开始初始化小园缴费平台数据库..."

# 检查MySQL连接
mysql -h${MYSQL_HOST} -P${MYSQL_PORT} -u${MYSQL_USER} -p${MYSQL_PASSWORD} -e "SELECT 1" >/dev/null 2>&1
if [ $? -ne 0 ]; then
    echo "错误：无法连接到MySQL数据库，请检查连接信息"
    exit 1
fi

echo "MySQL连接成功"

# 执行SQL脚本
echo "正在创建数据库表结构..."
mysql -h${MYSQL_HOST} -P${MYSQL_PORT} -u${MYSQL_USER} -p${MYSQL_PASSWORD} < create-tables.sql

if [ $? -eq 0 ]; then
    echo "数据库初始化成功！"
    echo ""
    echo "初始管理员账号信息："
    echo "用户名：admin"
    echo "密码：123456"
    echo ""
    echo "请及时修改初始密码！"
else
    echo "数据库初始化失败，请检查错误信息"
    exit 1
fi