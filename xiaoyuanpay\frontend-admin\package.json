{"name": "xiaoyuanpay-admin", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix", "format": "prettier --write src/"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "axios": "^1.5.0", "d3": "^7.8.5", "echarts": "^5.4.3", "element-plus": "^2.3.12", "pinia": "^2.1.6", "sass": "^1.89.2", "vue": "^3.3.4", "vue-router": "^4.5.1"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^8.35.1", "@typescript-eslint/parser": "^8.35.1", "@vitejs/plugin-vue": "^6.0.0", "@vue/tsconfig": "^0.7.0", "eslint": "^9.30.1", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.5.1", "eslint-plugin-vue": "^10.3.0", "prettier": "^3.6.2", "typescript": "~5.8.3", "vite": "^7.0.0", "vue-tsc": "^2.2.10"}}