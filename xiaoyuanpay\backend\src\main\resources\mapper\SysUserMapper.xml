<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xiaoyuan.pay.mapper.SysUserMapper">
    
    <!-- 根据用户ID获取角色编码列表 -->
    <select id="selectRoleCodesByUserId" resultType="java.lang.String">
        SELECT r.role_code
        FROM sys_role r
        INNER JOIN sys_user_role ur ON r.id = ur.role_id
        WHERE ur.user_id = #{userId}
        AND r.status = 1
        AND r.deleted = 0
    </select>
    
    <!-- 根据用户ID获取权限编码列表 -->
    <select id="selectPermissionCodesByUserId" resultType="java.lang.String">
        SELECT DISTINCT p.permission_code
        FROM sys_permission p
        INNER JOIN sys_role_permission rp ON p.id = rp.permission_id
        INNER JOIN sys_user_role ur ON rp.role_id = ur.role_id
        WHERE ur.user_id = #{userId}
        AND p.status = 1
        AND p.deleted = 0
    </select>
    
</mapper>