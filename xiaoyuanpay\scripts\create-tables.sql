-- 小园缴费平台数据库表结构
-- 创建数据库
CREATE DATABASE IF NOT EXISTS xiaoyuanpay_dev DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;

USE xiaoyuanpay_dev;

-- 1. 用户表
DROP TABLE IF EXISTS sys_user;
CREATE TABLE sys_user (
    id BIGINT NOT NULL COMMENT '用户ID',
    username VARCHAR(50) NOT NULL COMMENT '用户名',
    password VARCHAR(100) NOT NULL COMMENT '密码',
    real_name VARCHAR(100) COMMENT '真实姓名',
    phone VARCHAR(20) COMMENT '手机号',
    email VARCHAR(100) COMMENT '邮箱',
    avatar VARCHAR(200) COMMENT '头像',
    status TINYINT DEFAULT 1 COMMENT '状态：0-禁用，1-正常',
    last_login_time DATETIME COMMENT '最后登录时间',
    last_login_ip VARCHAR(50) COMMENT '最后登录IP',
    remark VARCHAR(500) COMMENT '备注',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted TINYINT DEFAULT 0 COMMENT '删除标志：0-正常，1-删除',
    PRIMARY KEY (id),
    UNIQUE KEY uk_username (username),
    KEY idx_phone (phone),
    KEY idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';

-- 2. 角色表
DROP TABLE IF EXISTS sys_role;
CREATE TABLE sys_role (
    id BIGINT NOT NULL COMMENT '角色ID',
    role_name VARCHAR(50) NOT NULL COMMENT '角色名称',
    role_code VARCHAR(50) NOT NULL COMMENT '角色编码',
    description VARCHAR(200) COMMENT '描述',
    status TINYINT DEFAULT 1 COMMENT '状态：0-禁用，1-正常',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted TINYINT DEFAULT 0 COMMENT '删除标志：0-正常，1-删除',
    PRIMARY KEY (id),
    UNIQUE KEY uk_role_code (role_code)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='角色表';

-- 3. 权限表
DROP TABLE IF EXISTS sys_permission;
CREATE TABLE sys_permission (
    id BIGINT NOT NULL COMMENT '权限ID',
    parent_id BIGINT DEFAULT 0 COMMENT '父权限ID',
    permission_name VARCHAR(50) NOT NULL COMMENT '权限名称',
    permission_code VARCHAR(100) NOT NULL COMMENT '权限编码',
    permission_type TINYINT NOT NULL COMMENT '权限类型：1-菜单，2-按钮',
    path VARCHAR(200) COMMENT '路由路径',
    component VARCHAR(200) COMMENT '组件路径',
    icon VARCHAR(50) COMMENT '图标',
    sort_order INT DEFAULT 0 COMMENT '排序',
    status TINYINT DEFAULT 1 COMMENT '状态：0-禁用，1-正常',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted TINYINT DEFAULT 0 COMMENT '删除标志：0-正常，1-删除',
    PRIMARY KEY (id),
    UNIQUE KEY uk_permission_code (permission_code),
    KEY idx_parent_id (parent_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='权限表';

-- 4. 用户角色关联表
DROP TABLE IF EXISTS sys_user_role;
CREATE TABLE sys_user_role (
    id BIGINT NOT NULL COMMENT 'ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    role_id BIGINT NOT NULL COMMENT '角色ID',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (id),
    UNIQUE KEY uk_user_role (user_id, role_id),
    KEY idx_role_id (role_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户角色关联表';

-- 5. 角色权限关联表
DROP TABLE IF EXISTS sys_role_permission;
CREATE TABLE sys_role_permission (
    id BIGINT NOT NULL COMMENT 'ID',
    role_id BIGINT NOT NULL COMMENT '角色ID',
    permission_id BIGINT NOT NULL COMMENT '权限ID',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (id),
    UNIQUE KEY uk_role_permission (role_id, permission_id),
    KEY idx_permission_id (permission_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='角色权限关联表';

-- 6. 学校表
DROP TABLE IF EXISTS school;
CREATE TABLE school (
    id BIGINT NOT NULL COMMENT '学校ID',
    school_name VARCHAR(100) NOT NULL COMMENT '学校名称',
    school_code VARCHAR(50) NOT NULL COMMENT '学校编码',
    province VARCHAR(50) COMMENT '省份',
    city VARCHAR(50) COMMENT '城市',
    district VARCHAR(50) COMMENT '区县',
    address VARCHAR(200) COMMENT '详细地址',
    contact_name VARCHAR(50) COMMENT '联系人',
    contact_phone VARCHAR(20) COMMENT '联系电话',
    status TINYINT DEFAULT 1 COMMENT '状态：0-禁用，1-正常',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted TINYINT DEFAULT 0 COMMENT '删除标志：0-正常，1-删除',
    PRIMARY KEY (id),
    UNIQUE KEY uk_school_code (school_code),
    KEY idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='学校表';

-- 7. 班级表
DROP TABLE IF EXISTS class;
CREATE TABLE class (
    id BIGINT NOT NULL COMMENT '班级ID',
    school_id BIGINT NOT NULL COMMENT '学校ID',
    grade_name VARCHAR(50) NOT NULL COMMENT '年级名称',
    class_name VARCHAR(50) NOT NULL COMMENT '班级名称',
    teacher_id BIGINT COMMENT '班主任ID',
    student_count INT DEFAULT 0 COMMENT '学生人数',
    status TINYINT DEFAULT 1 COMMENT '状态：0-禁用，1-正常',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted TINYINT DEFAULT 0 COMMENT '删除标志：0-正常，1-删除',
    PRIMARY KEY (id),
    KEY idx_school_id (school_id),
    KEY idx_teacher_id (teacher_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='班级表';

-- 8. 学生表
DROP TABLE IF EXISTS student;
CREATE TABLE student (
    id BIGINT NOT NULL COMMENT '学生ID',
    student_no VARCHAR(50) NOT NULL COMMENT '学号',
    student_name VARCHAR(50) NOT NULL COMMENT '学生姓名',
    class_id BIGINT NOT NULL COMMENT '班级ID',
    gender TINYINT COMMENT '性别：1-男，2-女',
    birthday DATE COMMENT '出生日期',
    parent_id BIGINT COMMENT '家长用户ID',
    status TINYINT DEFAULT 1 COMMENT '状态：0-禁用，1-正常',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted TINYINT DEFAULT 0 COMMENT '删除标志：0-正常，1-删除',
    PRIMARY KEY (id),
    UNIQUE KEY uk_student_no (student_no),
    KEY idx_class_id (class_id),
    KEY idx_parent_id (parent_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='学生表';

-- 9. 缴费项目表
DROP TABLE IF EXISTS payment_item;
CREATE TABLE payment_item (
    id BIGINT NOT NULL COMMENT '缴费项目ID',
    school_id BIGINT NOT NULL COMMENT '学校ID',
    item_name VARCHAR(100) NOT NULL COMMENT '项目名称',
    item_code VARCHAR(50) NOT NULL COMMENT '项目编码',
    category VARCHAR(50) NOT NULL COMMENT '类别：学费、杂费、兴趣班、活动费等',
    amount DECIMAL(10,2) NOT NULL COMMENT '金额',
    payment_type TINYINT NOT NULL COMMENT '缴费类型：1-固定费用，2-选择性费用',
    start_time DATETIME COMMENT '开始时间',
    end_time DATETIME COMMENT '结束时间',
    description TEXT COMMENT '描述',
    max_students INT COMMENT '最大人数限制（兴趣班）',
    current_students INT DEFAULT 0 COMMENT '当前报名人数',
    status TINYINT DEFAULT 1 COMMENT '状态：0-禁用，1-正常',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted TINYINT DEFAULT 0 COMMENT '删除标志：0-正常，1-删除',
    PRIMARY KEY (id),
    UNIQUE KEY uk_item_code (item_code),
    KEY idx_school_id (school_id),
    KEY idx_category (category),
    KEY idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='缴费项目表';

-- 10. 订单表
DROP TABLE IF EXISTS payment_order;
CREATE TABLE payment_order (
    id BIGINT NOT NULL COMMENT '订单ID',
    order_no VARCHAR(50) NOT NULL COMMENT '订单号',
    student_id BIGINT NOT NULL COMMENT '学生ID',
    user_id BIGINT NOT NULL COMMENT '下单用户ID',
    total_amount DECIMAL(10,2) NOT NULL COMMENT '订单总金额',
    discount_amount DECIMAL(10,2) DEFAULT 0 COMMENT '优惠金额',
    point_amount DECIMAL(10,2) DEFAULT 0 COMMENT '积分抵扣金额',
    actual_amount DECIMAL(10,2) NOT NULL COMMENT '实际支付金额',
    payment_method VARCHAR(50) COMMENT '支付方式：微信、支付宝、银联等',
    payment_status TINYINT DEFAULT 0 COMMENT '支付状态：0-待支付，1-已支付，2-已取消，3-已退款',
    payment_time DATETIME COMMENT '支付时间',
    transaction_id VARCHAR(100) COMMENT '第三方支付流水号',
    remark VARCHAR(500) COMMENT '备注',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted TINYINT DEFAULT 0 COMMENT '删除标志：0-正常，1-删除',
    PRIMARY KEY (id),
    UNIQUE KEY uk_order_no (order_no),
    KEY idx_student_id (student_id),
    KEY idx_user_id (user_id),
    KEY idx_payment_status (payment_status),
    KEY idx_create_time (create_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单表';

-- 11. 订单明细表
DROP TABLE IF EXISTS payment_order_detail;
CREATE TABLE payment_order_detail (
    id BIGINT NOT NULL COMMENT '明细ID',
    order_id BIGINT NOT NULL COMMENT '订单ID',
    payment_item_id BIGINT NOT NULL COMMENT '缴费项目ID',
    item_name VARCHAR(100) NOT NULL COMMENT '项目名称',
    amount DECIMAL(10,2) NOT NULL COMMENT '金额',
    quantity INT DEFAULT 1 COMMENT '数量',
    subtotal DECIMAL(10,2) NOT NULL COMMENT '小计',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (id),
    KEY idx_order_id (order_id),
    KEY idx_payment_item_id (payment_item_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单明细表';

-- 12. 退费申请表
DROP TABLE IF EXISTS refund_apply;
CREATE TABLE refund_apply (
    id BIGINT NOT NULL COMMENT '退费申请ID',
    refund_no VARCHAR(50) NOT NULL COMMENT '退费单号',
    order_id BIGINT NOT NULL COMMENT '原订单ID',
    student_id BIGINT NOT NULL COMMENT '学生ID',
    apply_user_id BIGINT NOT NULL COMMENT '申请人ID',
    refund_amount DECIMAL(10,2) NOT NULL COMMENT '退费金额',
    refund_reason VARCHAR(500) NOT NULL COMMENT '退费原因',
    status TINYINT DEFAULT 0 COMMENT '状态：0-待审核，1-审核通过，2-审核拒绝，3-已退款',
    audit_user_id BIGINT COMMENT '审核人ID',
    audit_time DATETIME COMMENT '审核时间',
    audit_remark VARCHAR(500) COMMENT '审核备注',
    refund_time DATETIME COMMENT '退款时间',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted TINYINT DEFAULT 0 COMMENT '删除标志：0-正常，1-删除',
    PRIMARY KEY (id),
    UNIQUE KEY uk_refund_no (refund_no),
    KEY idx_order_id (order_id),
    KEY idx_student_id (student_id),
    KEY idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='退费申请表';

-- 13. 积分表
DROP TABLE IF EXISTS user_point;
CREATE TABLE user_point (
    id BIGINT NOT NULL COMMENT '积分ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    total_points INT DEFAULT 0 COMMENT '总积分',
    available_points INT DEFAULT 0 COMMENT '可用积分',
    frozen_points INT DEFAULT 0 COMMENT '冻结积分',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id),
    UNIQUE KEY uk_user_id (user_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户积分表';

-- 14. 积分流水表
DROP TABLE IF EXISTS point_record;
CREATE TABLE point_record (
    id BIGINT NOT NULL COMMENT '流水ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    points INT NOT NULL COMMENT '积分数量',
    type TINYINT NOT NULL COMMENT '类型：1-获取，2-使用，3-转赠',
    source VARCHAR(50) NOT NULL COMMENT '来源：支付、签到、活动等',
    reference_id BIGINT COMMENT '关联ID（订单ID等）',
    description VARCHAR(200) COMMENT '描述',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (id),
    KEY idx_user_id (user_id),
    KEY idx_type (type),
    KEY idx_create_time (create_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='积分流水表';

-- 15. 操作日志表
DROP TABLE IF EXISTS sys_operation_log;
CREATE TABLE sys_operation_log (
    id BIGINT NOT NULL COMMENT '日志ID',
    user_id BIGINT COMMENT '用户ID',
    username VARCHAR(50) COMMENT '用户名',
    operation VARCHAR(100) COMMENT '操作内容',
    method VARCHAR(200) COMMENT '请求方法',
    params TEXT COMMENT '请求参数',
    result TEXT COMMENT '返回结果',
    ip VARCHAR(50) COMMENT 'IP地址',
    duration BIGINT COMMENT '执行时长(毫秒)',
    status TINYINT COMMENT '状态：0-失败，1-成功',
    error_msg TEXT COMMENT '错误信息',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (id),
    KEY idx_user_id (user_id),
    KEY idx_create_time (create_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='操作日志表';

-- 插入初始数据
-- 1. 插入超级管理员
INSERT INTO sys_user (id, username, password, real_name, phone, email, status) 
VALUES (1, 'admin', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBOsl7iI1o/CSi', 'Super Admin', '13800138000', '<EMAIL>', 1);

-- 2. 插入角色
INSERT INTO sys_role (id, role_name, role_code, description, status) VALUES 
(1, '超级管理员', 'SUPER_ADMIN', '系统超级管理员', 1),
(2, '学校管理员', 'SCHOOL_ADMIN', '学校管理员', 1),
(3, '财务管理员', 'FINANCE_ADMIN', '财务管理员', 1),
(4, '班主任', 'CLASS_TEACHER', '班主任', 1),
(5, '家长', 'PARENT', '学生家长', 1);

-- 3. 插入基础权限
INSERT INTO sys_permission (id, parent_id, permission_name, permission_code, permission_type, path, sort_order, status) VALUES 
(1, 0, '系统管理', 'system', 1, '/system', 1, 1),
(2, 1, '用户管理', 'system:user', 1, '/system/user', 1, 1),
(3, 1, '角色管理', 'system:role', 1, '/system/role', 2, 1),
(4, 1, '权限管理', 'system:permission', 1, '/system/permission', 3, 1),
(5, 0, '学校管理', 'school', 1, '/school', 2, 1),
(6, 5, '学校信息', 'school:info', 1, '/school/info', 1, 1),
(7, 5, '班级管理', 'school:class', 1, '/school/class', 2, 1),
(8, 5, '学生管理', 'school:student', 1, '/school/student', 3, 1),
(9, 0, '缴费管理', 'payment', 1, '/payment', 3, 1),
(10, 9, '缴费项目', 'payment:item', 1, '/payment/item', 1, 1),
(11, 9, '订单管理', 'payment:order', 1, '/payment/order', 2, 1),
(12, 9, '退费管理', 'payment:refund', 1, '/payment/refund', 3, 1);

-- 4. 分配超级管理员角色
INSERT INTO sys_user_role (id, user_id, role_id) VALUES (1, 1, 1);

-- 5. 分配超级管理员所有权限
INSERT INTO sys_role_permission (id, role_id, permission_id) 
SELECT ROW_NUMBER() OVER() + 1000, 1, id FROM sys_permission;