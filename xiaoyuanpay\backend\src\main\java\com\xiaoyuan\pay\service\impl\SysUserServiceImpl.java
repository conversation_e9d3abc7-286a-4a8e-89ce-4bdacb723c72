package com.xiaoyuan.pay.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xiaoyuan.pay.common.ResultCode;
import com.xiaoyuan.pay.dto.LoginRequest;
import com.xiaoyuan.pay.entity.SysUser;
import com.xiaoyuan.pay.exception.BusinessException;
import com.xiaoyuan.pay.mapper.SysUserMapper;
import com.xiaoyuan.pay.service.SysUserService;
import com.xiaoyuan.pay.utils.JwtUtil;
import com.xiaoyuan.pay.vo.LoginResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SysUserServiceImpl extends ServiceImpl<SysUserMapper, SysUser> implements SysUserService {
    
    private final PasswordEncoder passwordEncoder;
    private final JwtUtil jwtUtil;
    
    @Override
    @Transactional
    public LoginResponse login(LoginRequest loginRequest) {
        // 查询用户
        SysUser user = getByUsername(loginRequest.getUsername());
        if (user == null) {
            throw new BusinessException(ResultCode.USER_LOGIN_ERROR);
        }
        
        // 验证密码
        if (!passwordEncoder.matches(loginRequest.getPassword(), user.getPassword())) {
            throw new BusinessException(ResultCode.USER_LOGIN_ERROR);
        }
        
        // 检查用户状态
        if (user.getStatus() != 1) {
            throw new BusinessException(ResultCode.USER_ACCOUNT_FORBIDDEN);
        }
        
        // 生成token
        String token = jwtUtil.generateToken(user.getUsername());
        
        // 获取用户角色和权限
        List<String> roles = baseMapper.selectRoleCodesByUserId(user.getId());
        List<String> permissions = baseMapper.selectPermissionCodesByUserId(user.getId());
        
        // 更新最后登录时间
        user.setLastLoginTime(LocalDateTime.now());
        updateById(user);
        
        // 构建响应
        return LoginResponse.builder()
                .token(token)
                .userId(user.getId())
                .username(user.getUsername())
                .realName(user.getRealName())
                .avatar(user.getAvatar())
                .roles(roles)
                .permissions(permissions)
                .build();
    }
    
    @Override
    public SysUser getByUsername(String username) {
        LambdaQueryWrapper<SysUser> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysUser::getUsername, username);
        return getOne(wrapper);
    }
}