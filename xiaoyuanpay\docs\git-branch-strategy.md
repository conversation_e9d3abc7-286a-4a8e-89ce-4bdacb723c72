# Git 分支策略

## 分支说明

### 主要分支

- **main/master**: 生产环境分支，只接受来自release和hotfix的合并
- **develop**: 开发主分支，所有功能开发完成后合并到此分支

### 辅助分支

- **feature/\***: 功能开发分支
  - 命名规范：`feature/功能名称`
  - 例如：`feature/user-auth`、`feature/payment-module`
  
- **release/\***: 发布分支
  - 命名规范：`release/版本号`
  - 例如：`release/1.0.0`
  
- **hotfix/\***: 紧急修复分支
  - 命名规范：`hotfix/问题描述`
  - 例如：`hotfix/payment-bug`

## 开发流程

1. 从 develop 分支创建 feature 分支
```bash
git checkout develop
git checkout -b feature/new-feature
```

2. 功能开发完成后，合并回 develop
```bash
git checkout develop
git merge --no-ff feature/new-feature
git branch -d feature/new-feature
```

3. 准备发布时，从 develop 创建 release 分支
```bash
git checkout develop
git checkout -b release/1.0.0
```

4. 发布完成后，合并到 main 和 develop
```bash
git checkout main
git merge --no-ff release/1.0.0
git tag -a v1.0.0 -m "Release version 1.0.0"

git checkout develop
git merge --no-ff release/1.0.0
```

5. 紧急修复从 main 创建 hotfix 分支
```bash
git checkout main
git checkout -b hotfix/critical-bug
```

## 提交规范

使用 Conventional Commits 规范：

- feat: 新功能
- fix: 修复bug
- docs: 文档更新
- style: 代码格式调整
- refactor: 代码重构
- test: 测试相关
- chore: 构建过程或辅助工具的变动

示例：
```
feat: 添加用户登录功能
fix: 修复支付回调异常
docs: 更新API文档
```