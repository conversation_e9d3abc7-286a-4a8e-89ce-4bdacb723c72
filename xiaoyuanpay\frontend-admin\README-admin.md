# 小园缴费平台 - 管理端

## 技术栈

- Vue 3.3.4
- Vite 4.4.9
- Element Plus 2.3.12
- TypeScript 5.1.6
- Pinia 2.1.6
- Axios 1.5.0
- ECharts 5.4.3

## 项目结构

```
src/
├── api/            # API接口
├── assets/         # 静态资源
├── components/     # 公共组件
├── composables/    # 组合式函数
├── layouts/        # 布局组件
├── router/         # 路由配置
├── stores/         # Pinia状态管理
├── styles/         # 全局样式
├── utils/          # 工具函数
├── views/          # 页面组件
├── App.vue         # 根组件
└── main.ts         # 入口文件
```

## 开发

```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 构建生产版本
npm run build

# 代码检查
npm run lint

# 代码格式化
npm run format
```

## 开发规范

- 使用 Composition API
- 遵循 Vue 3 风格指南
- 使用 TypeScript 类型注解
- 组件命名使用 PascalCase
- 文件命名使用 kebab-case