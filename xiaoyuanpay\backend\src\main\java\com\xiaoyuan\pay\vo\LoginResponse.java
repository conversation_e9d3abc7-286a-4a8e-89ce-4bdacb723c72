package com.xiaoyuan.pay.vo;

import lombok.Builder;
import lombok.Data;
import java.util.List;

/**
 * 登录响应VO
 */
@Data
@Builder
public class LoginResponse {
    
    /**
     * 访问令牌
     */
    private String token;
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 用户名
     */
    private String username;
    
    /**
     * 真实姓名
     */
    private String realName;
    
    /**
     * 头像
     */
    private String avatar;
    
    /**
     * 角色列表
     */
    private List<String> roles;
    
    /**
     * 权限列表
     */
    private List<String> permissions;
}