-- 测试脚本
CREATE DATABASE IF NOT EXISTS xiaoyuanpay_dev DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;

USE xiaoyuanpay_dev;

-- 先删除表
DROP TABLE IF EXISTS sys_user;

-- 创建用户表
CREATE TABLE sys_user (
    id BIGINT NOT NULL,
    username VARCHAR(50) NOT NULL,
    password VARCHAR(100) NOT NULL,
    real_name VARCHAR(100),
    phone VARCHAR(20),
    email VARCHAR(100),
    avatar VARCHAR(200),
    status TINYINT DEFAULT 1,
    last_login_time DATETIME,
    last_login_ip VARCHAR(50),
    remark VARCHAR(500),
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted TINYINT DEFAULT 0,
    PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 测试插入
INSERT INTO sys_user (id, username, password, real_name, phone, email, status) 
VALUES (1, 'admin', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBOsl7iI1o/CSi', '超级管理员', '13800138000', '<EMAIL>', 1);

SELECT * FROM sys_user;