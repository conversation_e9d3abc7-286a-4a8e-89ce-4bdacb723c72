package com.xiaoyuan.pay.controller;

import com.xiaoyuan.pay.common.Result;
import com.xiaoyuan.pay.dto.LoginRequest;
import com.xiaoyuan.pay.service.SysUserService;
import com.xiaoyuan.pay.vo.LoginResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 认证控制器
 */
@Tag(name = "认证管理", description = "登录、注册、登出等接口")
@RestController
@RequestMapping("/auth")
@RequiredArgsConstructor
public class AuthController {
    
    private final SysUserService sysUserService;
    
    @Operation(summary = "用户登录")
    @PostMapping("/login")
    public Result<LoginResponse> login(@Validated @RequestBody LoginRequest loginRequest) {
        LoginResponse response = sysUserService.login(loginRequest);
        return Result.success("登录成功", response);
    }
    
    @Operation(summary = "用户登出")
    @PostMapping("/logout")
    public Result<Void> logout() {
        // 由于使用JWT，登出操作主要在前端清除token
        return Result.success("登出成功");
    }
}