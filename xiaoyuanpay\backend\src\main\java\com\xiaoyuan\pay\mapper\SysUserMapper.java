package com.xiaoyuan.pay.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xiaoyuan.pay.entity.SysUser;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 用户Mapper接口
 */
@Mapper
public interface SysUserMapper extends BaseMapper<SysUser> {
    
    /**
     * 根据用户ID获取角色编码列表
     */
    List<String> selectRoleCodesByUserId(@Param("userId") Long userId);
    
    /**
     * 根据用户ID获取权限编码列表
     */
    List<String> selectPermissionCodesByUserId(@Param("userId") Long userId);
}