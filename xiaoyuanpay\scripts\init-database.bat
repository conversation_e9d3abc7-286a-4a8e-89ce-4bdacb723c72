@echo off

rem Database connection info
set MYSQL_HOST=localhost
set MYSQL_PORT=3306
set MYSQL_USER=root
set MYSQL_PASSWORD=root
set DATABASE_NAME=xiaoyuanpay_dev

echo Starting XiaoYuanPay database initialization...

rem Check MySQL connection
mysql -h%MYSQL_HOST% -P%MYSQL_PORT% -u%MYSQL_USER% -p%MYSQL_PASSWORD% -e "SELECT 1" >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Cannot connect to MySQL database, please check connection info
    echo.
    echo Make sure MySQL is running and the credentials are correct:
    echo Host: %MYSQL_HOST%
    echo Port: %MYSQL_PORT%
    echo User: %MYSQL_USER%
    echo Password: %MYSQL_PASSWORD%
    pause
    exit /b 1
)

echo MySQL connection successful

rem Execute SQL script
echo Creating database tables...
mysql -h%MYSQL_HOST% -P%MYSQL_PORT% -u%MYSQL_USER% -p%MYSQL_PASSWORD% < init-xiaoyuanpay.sql

if %errorlevel% equ 0 (
    echo.
    echo Database initialization successful!
    echo.
    echo Initial admin account:
    echo Username: admin
    echo Password: 123456
    echo.
    echo Please change the initial password!
) else (
    echo Database initialization failed, please check error messages
    pause
    exit /b 1
)

pause