package com.xiaoyuan.pay.common;

import lombok.Getter;

/**
 * 响应码枚举
 */
@Getter
public enum ResultCode {
    
    SUCCESS(200, "操作成功"),
    FAILED(500, "操作失败"),
    
    // 参数错误：1001-1999
    PARAM_IS_INVALID(1001, "参数无效"),
    PARAM_IS_BLANK(1002, "参数为空"),
    PARAM_TYPE_BIND_ERROR(1003, "参数类型错误"),
    PARAM_NOT_COMPLETE(1004, "参数缺失"),
    
    // 用户错误：2001-2999
    USER_NOT_LOGGED_IN(2001, "用户未登录"),
    USER_LOGIN_ERROR(2002, "账号不存在或密码错误"),
    USER_ACCOUNT_FORBIDDEN(2003, "账号已被禁用"),
    USER_NOT_EXIST(2004, "用户不存在"),
    USER_HAS_EXISTED(2005, "用户已存在"),
    USER_PASSWORD_ERROR(2006, "密码错误"),
    USER_TOKEN_EXPIRED(2007, "token已过期"),
    USER_TOKEN_INVALID(2008, "token无效"),
    
    // 权限错误：3001-3999
    PERMISSION_NO_ACCESS(3001, "无访问权限"),
    PERMISSION_NO_OPERATE(3002, "无操作权限"),
    
    // 业务错误：4001-4999
    BUSINESS_ERROR(4001, "业务异常"),
    PAYMENT_ITEM_NOT_EXIST(4002, "缴费项目不存在"),
    PAYMENT_ITEM_DISABLED(4003, "缴费项目已禁用"),
    PAYMENT_ITEM_EXPIRED(4004, "缴费项目已过期"),
    PAYMENT_ITEM_FULL(4005, "缴费项目已满员"),
    ORDER_NOT_EXIST(4006, "订单不存在"),
    ORDER_STATUS_ERROR(4007, "订单状态异常"),
    ORDER_PAID(4008, "订单已支付"),
    ORDER_CANCELLED(4009, "订单已取消"),
    REFUND_NOT_ALLOWED(4010, "不允许退费"),
    REFUND_AMOUNT_ERROR(4011, "退费金额错误"),
    
    // 数据错误：5001-5999
    DATA_NOT_EXIST(5001, "数据不存在"),
    DATA_IS_EXISTED(5002, "数据已存在"),
    DATA_OPERATION_ERROR(5003, "数据操作异常"),
    
    // 系统错误：9001-9999
    SYSTEM_ERROR(9001, "系统异常"),
    UNKNOWN_ERROR(9999, "未知错误");
    
    private final Integer code;
    private final String message;
    
    ResultCode(Integer code, String message) {
        this.code = code;
        this.message = message;
    }
}