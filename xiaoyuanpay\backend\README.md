# 小园缴费平台 - 后端服务

## 技术栈

- Spring Boot 2.7.18
- MyBatis 3.5.13 + MyBatis-Plus 3.5.3.2
- MySQL 8.0.35
- Redis 6.2.14
- RocketMQ 4.9.7
- Spring Security + JWT
- Knife4j API文档

## 项目结构

```
src/
├── main/
│   ├── java/
│   │   └── com/xiaoyuan/pay/
│   │       ├── config/        # 配置类
│   │       ├── controller/    # 控制器
│   │       ├── service/       # 业务逻辑
│   │       ├── mapper/        # MyBatis Mapper
│   │       ├── entity/        # 实体类
│   │       ├── dto/           # 数据传输对象
│   │       ├── vo/            # 视图对象
│   │       ├── common/        # 公共类
│   │       ├── utils/         # 工具类
│   │       └── exception/     # 异常处理
│   └── resources/
│       ├── mapper/            # MyBatis XML
│       ├── application.yml    # 主配置
│       └── application-*.yml  # 环境配置
└── test/                      # 测试代码
```

## 快速启动

### 环境要求

- JDK 8+
- Maven 3.6+
- MySQL 8.0+
- Redis 6.2+

### 数据库初始化

```bash
mysql -u root -p < ../scripts/init-db.sql
```

### 启动服务

```bash
# 安装依赖
mvn clean install

# 运行服务
mvn spring-boot:run
```

### 访问地址

- API地址：http://localhost:8080/api
- API文档：http://localhost:8080/doc.html

## 开发规范

- 遵循阿里巴巴Java开发手册
- 使用统一的Result响应格式
- 所有API必须编写Swagger注解
- 敏感操作需要记录日志