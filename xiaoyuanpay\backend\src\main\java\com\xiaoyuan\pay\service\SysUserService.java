package com.xiaoyuan.pay.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xiaoyuan.pay.entity.SysUser;
import com.xiaoyuan.pay.dto.LoginRequest;
import com.xiaoyuan.pay.vo.LoginResponse;

/**
 * 用户服务接口
 */
public interface SysUserService extends IService<SysUser> {
    
    /**
     * 用户登录
     */
    LoginResponse login(LoginRequest loginRequest);
    
    /**
     * 根据用户名获取用户
     */
    SysUser getByUsername(String username);
}