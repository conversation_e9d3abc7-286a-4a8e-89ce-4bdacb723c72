-- XiaoYuanPay Database Initialization Script
-- Create database
CREATE DATABASE IF NOT EXISTS xiaoyuanpay_dev DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;

USE xiaoyuanpay_dev;

-- 1. User table
DROP TABLE IF EXISTS sys_user;
CREATE TABLE sys_user (
    id BIGINT NOT NULL COMMENT 'User ID',
    username VARCHAR(50) NOT NULL COMMENT 'Username',
    password VARCHAR(100) NOT NULL COMMENT 'Password',
    real_name VARCHAR(100) COMMENT 'Real name',
    phone VARCHAR(20) COMMENT 'Phone',
    email VARCHAR(100) COMMENT 'Email',
    avatar VARCHAR(200) COMMENT 'Avatar',
    status TINYINT DEFAULT 1 COMMENT 'Status: 0-disabled, 1-enabled',
    last_login_time DATETIME COMMENT 'Last login time',
    last_login_ip VARCHAR(50) COMMENT 'Last login IP',
    remark VARCHAR(500) COMMENT 'Remark',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT 'Create time',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Update time',
    deleted TINYINT DEFAULT 0 COMMENT 'Delete flag: 0-normal, 1-deleted',
    PRIMARY KEY (id),
    UNIQUE KEY uk_username (username),
    KEY idx_phone (phone),
    KEY idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='User table';

-- 2. Role table
DROP TABLE IF EXISTS sys_role;
CREATE TABLE sys_role (
    id BIGINT NOT NULL COMMENT 'Role ID',
    role_name VARCHAR(50) NOT NULL COMMENT 'Role name',
    role_code VARCHAR(50) NOT NULL COMMENT 'Role code',
    description VARCHAR(200) COMMENT 'Description',
    status TINYINT DEFAULT 1 COMMENT 'Status: 0-disabled, 1-enabled',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT 'Create time',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Update time',
    deleted TINYINT DEFAULT 0 COMMENT 'Delete flag: 0-normal, 1-deleted',
    PRIMARY KEY (id),
    UNIQUE KEY uk_role_code (role_code)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='Role table';

-- 3. Permission table
DROP TABLE IF EXISTS sys_permission;
CREATE TABLE sys_permission (
    id BIGINT NOT NULL COMMENT 'Permission ID',
    parent_id BIGINT DEFAULT 0 COMMENT 'Parent permission ID',
    permission_name VARCHAR(50) NOT NULL COMMENT 'Permission name',
    permission_code VARCHAR(100) NOT NULL COMMENT 'Permission code',
    permission_type TINYINT NOT NULL COMMENT 'Permission type: 1-menu, 2-button',
    path VARCHAR(200) COMMENT 'Route path',
    component VARCHAR(200) COMMENT 'Component path',
    icon VARCHAR(50) COMMENT 'Icon',
    sort_order INT DEFAULT 0 COMMENT 'Sort order',
    status TINYINT DEFAULT 1 COMMENT 'Status: 0-disabled, 1-enabled',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT 'Create time',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Update time',
    deleted TINYINT DEFAULT 0 COMMENT 'Delete flag: 0-normal, 1-deleted',
    PRIMARY KEY (id),
    UNIQUE KEY uk_permission_code (permission_code),
    KEY idx_parent_id (parent_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='Permission table';

-- 4. User-Role relation table
DROP TABLE IF EXISTS sys_user_role;
CREATE TABLE sys_user_role (
    id BIGINT NOT NULL COMMENT 'ID',
    user_id BIGINT NOT NULL COMMENT 'User ID',
    role_id BIGINT NOT NULL COMMENT 'Role ID',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT 'Create time',
    PRIMARY KEY (id),
    UNIQUE KEY uk_user_role (user_id, role_id),
    KEY idx_role_id (role_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='User-Role relation table';

-- 5. Role-Permission relation table
DROP TABLE IF EXISTS sys_role_permission;
CREATE TABLE sys_role_permission (
    id BIGINT NOT NULL COMMENT 'ID',
    role_id BIGINT NOT NULL COMMENT 'Role ID',
    permission_id BIGINT NOT NULL COMMENT 'Permission ID',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT 'Create time',
    PRIMARY KEY (id),
    UNIQUE KEY uk_role_permission (role_id, permission_id),
    KEY idx_permission_id (permission_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='Role-Permission relation table';

-- 6. School table
DROP TABLE IF EXISTS school;
CREATE TABLE school (
    id BIGINT NOT NULL COMMENT 'School ID',
    school_name VARCHAR(100) NOT NULL COMMENT 'School name',
    school_code VARCHAR(50) NOT NULL COMMENT 'School code',
    province VARCHAR(50) COMMENT 'Province',
    city VARCHAR(50) COMMENT 'City',
    district VARCHAR(50) COMMENT 'District',
    address VARCHAR(200) COMMENT 'Address',
    contact_name VARCHAR(50) COMMENT 'Contact name',
    contact_phone VARCHAR(20) COMMENT 'Contact phone',
    status TINYINT DEFAULT 1 COMMENT 'Status: 0-disabled, 1-enabled',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT 'Create time',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Update time',
    deleted TINYINT DEFAULT 0 COMMENT 'Delete flag: 0-normal, 1-deleted',
    PRIMARY KEY (id),
    UNIQUE KEY uk_school_code (school_code),
    KEY idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='School table';

-- 7. Class table
DROP TABLE IF EXISTS class;
CREATE TABLE class (
    id BIGINT NOT NULL COMMENT 'Class ID',
    school_id BIGINT NOT NULL COMMENT 'School ID',
    grade_name VARCHAR(50) NOT NULL COMMENT 'Grade name',
    class_name VARCHAR(50) NOT NULL COMMENT 'Class name',
    teacher_id BIGINT COMMENT 'Class teacher ID',
    student_count INT DEFAULT 0 COMMENT 'Student count',
    status TINYINT DEFAULT 1 COMMENT 'Status: 0-disabled, 1-enabled',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT 'Create time',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Update time',
    deleted TINYINT DEFAULT 0 COMMENT 'Delete flag: 0-normal, 1-deleted',
    PRIMARY KEY (id),
    KEY idx_school_id (school_id),
    KEY idx_teacher_id (teacher_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='Class table';

-- 8. Student table
DROP TABLE IF EXISTS student;
CREATE TABLE student (
    id BIGINT NOT NULL COMMENT 'Student ID',
    student_no VARCHAR(50) NOT NULL COMMENT 'Student number',
    student_name VARCHAR(50) NOT NULL COMMENT 'Student name',
    class_id BIGINT NOT NULL COMMENT 'Class ID',
    gender TINYINT COMMENT 'Gender: 1-male, 2-female',
    birthday DATE COMMENT 'Birthday',
    parent_id BIGINT COMMENT 'Parent user ID',
    status TINYINT DEFAULT 1 COMMENT 'Status: 0-disabled, 1-enabled',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT 'Create time',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Update time',
    deleted TINYINT DEFAULT 0 COMMENT 'Delete flag: 0-normal, 1-deleted',
    PRIMARY KEY (id),
    UNIQUE KEY uk_student_no (student_no),
    KEY idx_class_id (class_id),
    KEY idx_parent_id (parent_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='Student table';

-- 9. Payment item table
DROP TABLE IF EXISTS payment_item;
CREATE TABLE payment_item (
    id BIGINT NOT NULL COMMENT 'Payment item ID',
    school_id BIGINT NOT NULL COMMENT 'School ID',
    item_name VARCHAR(100) NOT NULL COMMENT 'Item name',
    item_code VARCHAR(50) NOT NULL COMMENT 'Item code',
    category VARCHAR(50) NOT NULL COMMENT 'Category',
    amount DECIMAL(10,2) NOT NULL COMMENT 'Amount',
    payment_type TINYINT NOT NULL COMMENT 'Payment type: 1-fixed, 2-optional',
    start_time DATETIME COMMENT 'Start time',
    end_time DATETIME COMMENT 'End time',
    description TEXT COMMENT 'Description',
    max_students INT COMMENT 'Max students',
    current_students INT DEFAULT 0 COMMENT 'Current students',
    status TINYINT DEFAULT 1 COMMENT 'Status: 0-disabled, 1-enabled',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT 'Create time',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Update time',
    deleted TINYINT DEFAULT 0 COMMENT 'Delete flag: 0-normal, 1-deleted',
    PRIMARY KEY (id),
    UNIQUE KEY uk_item_code (item_code),
    KEY idx_school_id (school_id),
    KEY idx_category (category),
    KEY idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='Payment item table';

-- 10. Order table
DROP TABLE IF EXISTS payment_order;
CREATE TABLE payment_order (
    id BIGINT NOT NULL COMMENT 'Order ID',
    order_no VARCHAR(50) NOT NULL COMMENT 'Order number',
    student_id BIGINT NOT NULL COMMENT 'Student ID',
    user_id BIGINT NOT NULL COMMENT 'User ID',
    total_amount DECIMAL(10,2) NOT NULL COMMENT 'Total amount',
    discount_amount DECIMAL(10,2) DEFAULT 0 COMMENT 'Discount amount',
    point_amount DECIMAL(10,2) DEFAULT 0 COMMENT 'Point deduction amount',
    actual_amount DECIMAL(10,2) NOT NULL COMMENT 'Actual amount',
    payment_method VARCHAR(50) COMMENT 'Payment method',
    payment_status TINYINT DEFAULT 0 COMMENT 'Payment status: 0-pending, 1-paid, 2-cancelled, 3-refunded',
    payment_time DATETIME COMMENT 'Payment time',
    transaction_id VARCHAR(100) COMMENT 'Transaction ID',
    remark VARCHAR(500) COMMENT 'Remark',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT 'Create time',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Update time',
    deleted TINYINT DEFAULT 0 COMMENT 'Delete flag: 0-normal, 1-deleted',
    PRIMARY KEY (id),
    UNIQUE KEY uk_order_no (order_no),
    KEY idx_student_id (student_id),
    KEY idx_user_id (user_id),
    KEY idx_payment_status (payment_status),
    KEY idx_create_time (create_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='Order table';

-- 11. Order detail table
DROP TABLE IF EXISTS payment_order_detail;
CREATE TABLE payment_order_detail (
    id BIGINT NOT NULL COMMENT 'Detail ID',
    order_id BIGINT NOT NULL COMMENT 'Order ID',
    payment_item_id BIGINT NOT NULL COMMENT 'Payment item ID',
    item_name VARCHAR(100) NOT NULL COMMENT 'Item name',
    amount DECIMAL(10,2) NOT NULL COMMENT 'Amount',
    quantity INT DEFAULT 1 COMMENT 'Quantity',
    subtotal DECIMAL(10,2) NOT NULL COMMENT 'Subtotal',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT 'Create time',
    PRIMARY KEY (id),
    KEY idx_order_id (order_id),
    KEY idx_payment_item_id (payment_item_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='Order detail table';

-- 12. Refund apply table
DROP TABLE IF EXISTS refund_apply;
CREATE TABLE refund_apply (
    id BIGINT NOT NULL COMMENT 'Refund apply ID',
    refund_no VARCHAR(50) NOT NULL COMMENT 'Refund number',
    order_id BIGINT NOT NULL COMMENT 'Order ID',
    student_id BIGINT NOT NULL COMMENT 'Student ID',
    apply_user_id BIGINT NOT NULL COMMENT 'Apply user ID',
    refund_amount DECIMAL(10,2) NOT NULL COMMENT 'Refund amount',
    refund_reason VARCHAR(500) NOT NULL COMMENT 'Refund reason',
    status TINYINT DEFAULT 0 COMMENT 'Status: 0-pending, 1-approved, 2-rejected, 3-refunded',
    audit_user_id BIGINT COMMENT 'Audit user ID',
    audit_time DATETIME COMMENT 'Audit time',
    audit_remark VARCHAR(500) COMMENT 'Audit remark',
    refund_time DATETIME COMMENT 'Refund time',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT 'Create time',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Update time',
    deleted TINYINT DEFAULT 0 COMMENT 'Delete flag: 0-normal, 1-deleted',
    PRIMARY KEY (id),
    UNIQUE KEY uk_refund_no (refund_no),
    KEY idx_order_id (order_id),
    KEY idx_student_id (student_id),
    KEY idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='Refund apply table';

-- 13. User point table
DROP TABLE IF EXISTS user_point;
CREATE TABLE user_point (
    id BIGINT NOT NULL COMMENT 'Point ID',
    user_id BIGINT NOT NULL COMMENT 'User ID',
    total_points INT DEFAULT 0 COMMENT 'Total points',
    available_points INT DEFAULT 0 COMMENT 'Available points',
    frozen_points INT DEFAULT 0 COMMENT 'Frozen points',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT 'Create time',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Update time',
    PRIMARY KEY (id),
    UNIQUE KEY uk_user_id (user_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='User point table';

-- 14. Point record table
DROP TABLE IF EXISTS point_record;
CREATE TABLE point_record (
    id BIGINT NOT NULL COMMENT 'Record ID',
    user_id BIGINT NOT NULL COMMENT 'User ID',
    points INT NOT NULL COMMENT 'Points',
    type TINYINT NOT NULL COMMENT 'Type: 1-earn, 2-use, 3-transfer',
    source VARCHAR(50) NOT NULL COMMENT 'Source',
    reference_id BIGINT COMMENT 'Reference ID',
    description VARCHAR(200) COMMENT 'Description',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT 'Create time',
    PRIMARY KEY (id),
    KEY idx_user_id (user_id),
    KEY idx_type (type),
    KEY idx_create_time (create_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='Point record table';

-- 15. Operation log table
DROP TABLE IF EXISTS sys_operation_log;
CREATE TABLE sys_operation_log (
    id BIGINT NOT NULL COMMENT 'Log ID',
    user_id BIGINT COMMENT 'User ID',
    username VARCHAR(50) COMMENT 'Username',
    operation VARCHAR(100) COMMENT 'Operation',
    method VARCHAR(200) COMMENT 'Method',
    params TEXT COMMENT 'Parameters',
    result TEXT COMMENT 'Result',
    ip VARCHAR(50) COMMENT 'IP address',
    duration BIGINT COMMENT 'Duration (ms)',
    status TINYINT COMMENT 'Status: 0-failed, 1-success',
    error_msg TEXT COMMENT 'Error message',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT 'Create time',
    PRIMARY KEY (id),
    KEY idx_user_id (user_id),
    KEY idx_create_time (create_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='Operation log table';

-- Insert initial data
-- 1. Insert super admin (password: 123456)
INSERT INTO sys_user (id, username, password, real_name, phone, email, status) 
VALUES (1, 'admin', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBOsl7iI1o/CSi', 'Administrator', '13800138000', '<EMAIL>', 1);

-- 2. Insert roles
INSERT INTO sys_role (id, role_name, role_code, description, status) VALUES 
(1, 'Super Admin', 'SUPER_ADMIN', 'System super administrator', 1),
(2, 'School Admin', 'SCHOOL_ADMIN', 'School administrator', 1),
(3, 'Finance Admin', 'FINANCE_ADMIN', 'Finance administrator', 1),
(4, 'Class Teacher', 'CLASS_TEACHER', 'Class teacher', 1),
(5, 'Parent', 'PARENT', 'Student parent', 1);

-- 3. Insert permissions
INSERT INTO sys_permission (id, parent_id, permission_name, permission_code, permission_type, path, sort_order, status) VALUES 
(1, 0, 'System Management', 'system', 1, '/system', 1, 1),
(2, 1, 'User Management', 'system:user', 1, '/system/user', 1, 1),
(3, 1, 'Role Management', 'system:role', 1, '/system/role', 2, 1),
(4, 1, 'Permission Management', 'system:permission', 1, '/system/permission', 3, 1),
(5, 0, 'School Management', 'school', 1, '/school', 2, 1),
(6, 5, 'School Info', 'school:info', 1, '/school/info', 1, 1),
(7, 5, 'Class Management', 'school:class', 1, '/school/class', 2, 1),
(8, 5, 'Student Management', 'school:student', 1, '/school/student', 3, 1),
(9, 0, 'Payment Management', 'payment', 1, '/payment', 3, 1),
(10, 9, 'Payment Items', 'payment:item', 1, '/payment/item', 1, 1),
(11, 9, 'Order Management', 'payment:order', 1, '/payment/order', 2, 1),
(12, 9, 'Refund Management', 'payment:refund', 1, '/payment/refund', 3, 1);

-- 4. Assign super admin role
INSERT INTO sys_user_role (id, user_id, role_id) VALUES (1, 1, 1);

-- 5. Assign all permissions to super admin
INSERT INTO sys_role_permission (id, role_id, permission_id) 
SELECT 1000 + id, 1, id FROM sys_permission;

-- Show result
SELECT 'Database initialization completed successfully!' AS result;